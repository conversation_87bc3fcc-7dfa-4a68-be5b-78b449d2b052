{"name": "FxDPartnerERP", "private": true, "version": "0.1.0", "type": "module", "homepage": "https://bindalkapil.github.io/FxDPartnerERP", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:reset": "supabase db reset", "supabase:diff": "supabase db diff", "supabase:push": "supabase db push", "supabase:pull": "supabase db pull", "supabase:generate-types": "supabase gen types typescript --local > src/lib/database.types.ts", "supabase:setup": "chmod +x scripts/setup-supabase-cli.sh && ./scripts/setup-supabase-cli.sh", "supabase:deploy": "chmod +x scripts/deploy-migration.sh && ./scripts/deploy-migration.sh", "supabase:verify": "chmod +x scripts/verify-migration.sh && ./scripts/verify-migration.sh", "supabase:migration-list": "supabase migration list"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@supabase/supabase-js": "^2.39.0", "@types/react-datepicker": "^6.2.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "gh-pages": "^6.3.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "typescript-eslint": "^8.0.0", "vite": "^5.0.10"}}