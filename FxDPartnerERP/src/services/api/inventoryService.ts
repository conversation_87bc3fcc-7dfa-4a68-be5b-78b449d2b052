import { apiRequest } from './config';

export const inventoryService = {
  // Get current inventory
  getCurrent: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory', {}, showGlobalError);
  },

  // Get available inventory
  getAvailable: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory/available', {}, showGlobalError);
  },

  // Adjust inventory
  adjust: async (data: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory/adjust', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update inventory item
  update: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/inventory/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Adjust inventory as another SKU
  adjustAsAnotherSKU: async (currentProductId: string, currentSkuId: string, newProductId: string, newSkuId: string, reason: string, showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory/adjust-as-another-sku', {
      method: 'POST',
      body: JSON.stringify({
        currentProductId,
        currentSkuId,
        newProductId,
        newSkuId,
        reason
      }),
    }, showGlobalError);
  },

  // Mark inventory as dump
  markAsDump: async (data: {
    product_id: string;
    sku_id: string;
    quantity: number;
    weight?: number;
    reason: string;
    reason_notes?: string;
  }, showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory/mark-dump', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update SKU details
  updateSKU: async (data: {
    current_product_id: string;
    current_sku_id: string;
    new_sku_code?: string;
    new_unit_type?: string;
    new_unit_weight?: number;
    reason: string;
  }, showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory/update-sku', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Manual inventory adjustment
  manualAdjust: async (data: {
    product_id: string;
    sku_id: string;
    adjustment_type: 'add' | 'subtract' | 'set';
    quantity_change: number;
    weight_change?: number;
    reason: string;
    notes?: string;
  }, showGlobalError: boolean = true) => {
    return await apiRequest('/api/inventory/manual-adjust', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Get inventory dumps
  getDumps: async (filters?: {
    startDate?: string;
    endDate?: string;
    reason?: string;
    productId?: string;
    skuId?: string;
  }, showGlobalError: boolean = true) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    if (filters?.reason) queryParams.append('reason', filters.reason);
    if (filters?.productId) queryParams.append('productId', filters.productId);
    if (filters?.skuId) queryParams.append('skuId', filters.skuId);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/inventory/dumps?${queryString}` : '/api/inventory/dumps';
    
    return await apiRequest(url, {}, showGlobalError);
  },

  // Get adjustment reports
  getAdjustmentReports: async (filters?: {
    startDate?: string;
    endDate?: string;
    type?: string;
    productId?: string;
    skuId?: string;
  }, showGlobalError: boolean = true) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    if (filters?.type) queryParams.append('type', filters.type);
    if (filters?.productId) queryParams.append('productId', filters.productId);
    if (filters?.skuId) queryParams.append('skuId', filters.skuId);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/inventory/adjustments/report?${queryString}` : '/api/inventory/adjustments/report';
    
    return await apiRequest(url, {}, showGlobalError);
  },

  // Search inventory
  search: async (query: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/inventory/search?q=${encodeURIComponent(query)}`, {}, showGlobalError);
  },

  // Get inventory history
  getHistory: async (productId: string, skuId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/inventory/history/${productId}/${skuId}`, {}, showGlobalError);
  },
};
