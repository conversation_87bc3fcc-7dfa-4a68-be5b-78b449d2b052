// Type definitions for API responses and data structures

export interface Customer {
  id: string;
  organization_id: string;
  name: string;
  customer_type: 'individual' | 'business';
  contact: string;
  email: string;
  address: string;
  delivery_addresses: any[] | null;
  gst_number?: string;
  pan_number?: string;
  credit_limit: number;
  current_balance: number;
  payment_terms: number;
  status: 'active' | 'inactive';
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Supplier {
  id: string;
  organization_id: string;
  company_name: string;
  reference_number: string;
  contact_person: string;
  phone: string;
  email: string;
  address: string;
  gst_number?: string;
  pan_number?: string;
  bank_name?: string;
  account_number?: string;
  ifsc_code?: string;
  payment_terms: number;
  credit_limit: number;
  current_balance: number;
  products: any[] | null;
  status: 'active' | 'inactive';
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCustomerData {
  name: string;
  customer_type?: 'individual' | 'business';
  contact: string;
  email: string;
  address: string;
  delivery_addresses?: any[];
  gst_number?: string;
  pan_number?: string;
  credit_limit?: number;
  payment_terms?: number;
  notes?: string;
}

export interface CreateSupplierData {
  company_name: string;
  reference_number: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  gst_number?: string;
  pan_number?: string;
  bank_name?: string;
  account_number?: string;
  ifsc_code?: string;
  payment_terms?: number;
  credit_limit?: number;
  products?: any[];
  notes?: string;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  current_balance?: number;
  status?: 'active' | 'inactive';
}

export interface UpdateSupplierData extends Partial<CreateSupplierData> {
  current_balance?: number;
  status?: 'active' | 'inactive';
}

export interface SalesOrder {
  id: string;
  order_number: string;
  customer_id: string;
  order_date: string;
  delivery_date?: string;
  delivery_address?: string;
  payment_terms: number;
  payment_mode: string;
  payment_status: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  items?: SalesOrderItem[];
}

export interface SalesOrderItem {
  id: string;
  sales_order_id: string;
  product_id: string;
  sku_id: string;
  product_name: string;
  sku_code: string;
  quantity: number;
  unit_type: string;
  unit_price: number;
  total_price: number;
}

export interface PurchaseRecord {
  id: string;
  record_number: string;
  supplier_id: string;
  record_date: string;
  arrival_timestamp: string;
  pricing_model: string;
  total_amount: number;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  items?: PurchaseRecordItem[];
  costs?: PurchaseRecordCost[];
}

export interface PurchaseRecordItem {
  id: string;
  purchase_record_id: string;
  product_id: string;
  sku_id: string;
  product_name: string;
  sku_code: string;
  quantity: number;
  unit_type: string;
  unit_price: number;
  total_price: number;
}

export interface PurchaseRecordCost {
  id: string;
  purchase_record_id: string;
  cost_type: string;
  description: string;
  amount: number;
}

export interface Payment {
  id: string;
  type: 'received' | 'paid';
  amount: number;
  payment_date: string;
  party_id: string;
  party_type: 'customer' | 'supplier';
  party_name: string;
  reference_id?: string;
  reference_type?: string;
  reference_number?: string;
  mode: string;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

export interface BalanceUpdateRequest {
  amount: number;
  operation: 'add' | 'subtract';
}
