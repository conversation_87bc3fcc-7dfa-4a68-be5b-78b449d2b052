import React from 'react';
import { 
  Search
} from 'lucide-react';
import { PaymentFilters as PaymentFiltersType } from '../../types/payment.types';
import Dropdown from '../ui/Dropdown';

interface PaymentFiltersProps {
  filters: PaymentFiltersType;
  onUpdateFilter: <K extends keyof PaymentFiltersType>(key: K, value: PaymentFiltersType[K]) => void;
  onUpdateDateRange: (from: string, to: string) => void;
  onUpdateAmountRange: (min: number | null, max: number | null) => void;
  onClearFilters: () => void;
  onSetQuickDateFilter: (period: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom') => void;
  activeFiltersCount: number;
  hasActiveFilters: boolean;
  filterOptions: {
    modes: string[];
    partyTypes: string[];
    statuses: string[];
  };
}

const PaymentFilters: React.FC<PaymentFiltersProps> = ({
  filters,
  onUpdateFilter,
  onUpdateDateRange,
  onSetQuickDateFilter,
  filterOptions
}) => {

  const formatModeDisplay = (mode: string) => {
    switch (mode) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'upi':
        return 'UPI';
      default:
        return mode.charAt(0).toUpperCase() + mode.slice(1);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
      {/* Single Line with Search and All Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center gap-4">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
            placeholder="Search payments, parties, references..."
            value={filters.search}
            onChange={(e) => onUpdateFilter('search', e.target.value)}
          />
        </div>

        {/* All Filters in Single Row */}
        <div className="flex flex-wrap gap-3 items-center">
          {/* Payment Type */}
          <div className="min-w-[140px]">
            <Dropdown
              options={[
                { value: 'all', label: 'All Types' },
                { value: 'received', label: 'Received' },
                { value: 'made', label: 'Made' },
                { value: 'expense', label: 'Expense' }
              ]}
              value={filters.type}
              onChange={(value) => onUpdateFilter('type', value as PaymentFiltersType['type'])}
              className="text-sm w-full"
            />
          </div>

          {/* Party Type */}
          <div className="min-w-[160px]">
            <Dropdown
              options={[
                { value: 'all', label: 'All Party Types' },
                { value: 'customer', label: 'Customer' },
                { value: 'supplier', label: 'Supplier' }
              ]}
              value={filters.partyType}
              onChange={(value) => onUpdateFilter('partyType', value as PaymentFiltersType['partyType'])}
              className="text-sm w-full"
            />
          </div>

          {/* Payment Mode */}
          <div className="min-w-[140px]">
            <Dropdown
              options={[
                { value: 'all', label: 'All Modes' },
                ...filterOptions.modes.map((mode) => ({
                  value: mode,
                  label: formatModeDisplay(mode)
                }))
              ]}
              value={filters.mode}
              onChange={(value) => onUpdateFilter('mode', value as PaymentFiltersType['mode'])}
              className="text-sm w-full"
            />
          </div>

          {/* Date Range Filter */}
          <div className="min-w-[160px]">
            <Dropdown
              options={[
                { value: 'today', label: 'Today' },
                { value: 'yesterday', label: 'Yesterday' },
                { value: 'week', label: 'Last Week' },
                { value: 'month', label: 'Last Month' },
                { value: 'quarter', label: 'Last Quarter' },
                { value: 'year', label: 'This Year' },
                { value: 'custom', label: 'Custom Range' }
              ]}
              value={filters.quickDateFilter || 'today'}
              onChange={(value) => onSetQuickDateFilter(value as 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom')}
              className="text-sm w-full"
            />
          </div>

          {/* Custom Date Range - Only show when custom is selected */}
          {filters.quickDateFilter === 'custom' && (
            <>
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">From:</label>
                <input
                  type="date"
                  value={filters.dateRange.from}
                  onChange={(e) => onUpdateDateRange(e.target.value, filters.dateRange.to)}
                  className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">To:</label>
                <input
                  type="date"
                  value={filters.dateRange.to}
                  onChange={(e) => onUpdateDateRange(filters.dateRange.from, e.target.value)}
                  className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </>
          )}

        </div>
      </div>
    </div>
  );
};

export default PaymentFilters;
