/**
 * Utility functions for formatting weight and quantity values
 */

/**
 * Formats a weight/quantity value with appropriate decimal places
 * - Shows whole numbers without decimals (30 → "30")
 * - Shows decimal numbers with their decimal places (30.12 → "30.12")
 * - Removes trailing zeros (30.00 → "30")
 */
export const formatQuantity = (value: number | null | undefined): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0';
  }
  
  // Convert to number and handle precision issues
  const numValue = Number(value);
  
  // If it's a whole number, return without decimals
  if (numValue % 1 === 0) {
    return numValue.toString();
  }
  
  // Otherwise, return with decimals but remove trailing zeros
  return numValue.toString();
};

/**
 * Formats weight/quantity with unit display
 * @param value - The numeric value to format
 * @param unitType - The unit type ('box' or 'loose')
 * @param includeUnit - Whether to include the unit text (default: true)
 */
export const formatWeightWithUnit = (
  value: number | null | undefined, 
  unitType: string, 
  includeUnit: boolean = true
): string => {
  const formattedValue = formatQuantity(value);
  
  if (!includeUnit) {
    return formattedValue;
  }
  
  const unit = unitType === 'box' ? 'boxes' : 'kg';
  return `${formattedValue} ${unit}`;
};

/**
 * Formats weight specifically for display (always shows 'kg' regardless of unit type)
 * Used for total weight displays
 */
export const formatWeight = (value: number | null | undefined): string => {
  const formattedValue = formatQuantity(value);
  return `${formattedValue} kg`;
};

/**
 * Formats quantity based on unit type for display
 * @param value - The numeric value to format
 * @param unitType - The unit type ('box' or 'loose')
 */
export const formatQuantityWithUnit = (
  value: number | null | undefined,
  unitType: string
): string => {
  return formatWeightWithUnit(value, unitType, true);
};

/**
 * Helper function to get unit display text
 */
export const getUnitText = (unitType: string): string => {
  return unitType === 'box' ? 'boxes' : 'kg';
};

/**
 * Formats a range of quantities (e.g., "30/35 kg" or "30.12/35.5 boxes")
 */
export const formatQuantityRange = (
  currentValue: number | null | undefined,
  totalValue: number | null | undefined,
  unitType: string
): string => {
  const current = formatQuantity(currentValue);
  const total = formatQuantity(totalValue);
  const unit = getUnitText(unitType);
  
  return `${current}/${total} ${unit}`;
