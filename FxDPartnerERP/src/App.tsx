import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SidebarProvider } from './contexts/SidebarContext';
import { BrowserRouter as Router } from 'react-router-dom'

// Layouts
import DashboardLayout from './layouts/DashboardLayout';

// Pages
import Login from './pages/auth/Login';
import Dashboard from './pages/dashboard/Dashboard';
import VehicleArrival from './pages/procurement/VehicleArrival';
import NewVehicleArrival from './pages/procurement/NewVehicleArrival';
import ViewVehicleArrival from './pages/procurement/ViewVehicleArrival';
import EditVehicleArrival from './pages/procurement/EditVehicleArrival';
import RecordPurchase from './pages/procurement/RecordPurchase';
import NewRecordPurchase from './pages/procurement/NewRecordPurchase';
import ViewRecordPurchase from './pages/procurement/ViewRecordPurchase';
import EditRecordPurchase from './pages/procurement/EditRecordPurchase';
import Inventory from './pages/inventory/Inventory';
import Sales from './pages/sales/Sales';
import NewSale from './pages/sales/NewSale';
import ViewSale from './pages/sales/ViewSale';
import EditSale from './pages/sales/EditSale';
import Dispatch from './pages/sales/Dispatch';
import Suppliers from './pages/partners/Suppliers';
import AddSupplier from './pages/partners/AddSupplier';
import ViewSupplier from './pages/partners/ViewSupplier';
import EditSupplier from './pages/partners/EditSupplier';
import Customers from './pages/partners/Customers';
import AddCustomer from './pages/partners/AddCustomer';
import ViewCustomer from './pages/partners/ViewCustomer';
import EditCustomer from './pages/partners/EditCustomer';
import Transactions from './pages/finance/Transactions';
import Payments from './pages/finance/Payments';
import NotFound from './pages/NotFound';

// Admin components
import { AdminProvider, useAdmin } from './admin/contexts/AdminContext';
import AdminLogin from './admin/pages/AdminLogin';
import AdminLayout from './admin/components/AdminLayout';
import AdminDashboard from './admin/pages/AdminDashboard';
import OrganizationsList from './admin/pages/organizations/OrganizationsList';
import UsersList from './admin/pages/users/UsersList';
import AddUser from './admin/pages/users/AddUser';
import UserPermissions from './admin/pages/users/UserPermissions';
import EditUser from './admin/pages/users/EditUser';
// ROLES HIDDEN - Role components kept for future use but not routed
import RolesList from './admin/pages/roles/RolesList';
import AddRole from './admin/pages/roles/AddRole';
import EditRole from './admin/pages/roles/EditRole';
import RolePermissions from './admin/pages/roles/RolePermissions';

// Auth Guard Component
const PrivateRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// Admin Auth Guard Component
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, loading } = useAdmin();
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    return <AdminLogin />;
  }
  
  return <>{children}</>;
};

function App() {
  return (
    <AdminProvider>
      <AuthProvider>
        <SidebarProvider>
          <Router>
            <Toaster position="top-right" />
            <Routes>
              <Route path="/login" element={<Login />} />
              
              {/* Admin Routes - Must come before main app routes */}
              <Route 
                path="/admin/*" 
                element={
                  <AdminRoute>
                    <AdminLayout />
                  </AdminRoute>
                }
              >
                <Route index element={<AdminDashboard />} />
                <Route path="organizations" element={<OrganizationsList />} />
                <Route path="users" element={<UsersList />} />
                <Route path="users/create" element={<AddUser />} />
                <Route path="users/:userId/edit" element={<EditUser />} />
                <Route path="users/:userId/permissions" element={<UserPermissions />} />
                {/* ROLES HIDDEN - Uncomment below to re-enable role routes
                <Route path="roles" element={<RolesList />} />
                <Route path="roles/create" element={<AddRole />} />
                <Route path="roles/:id/edit" element={<EditRole />} />
                <Route path="roles/:id/permissions" element={<RolePermissions />} />
                */}
              </Route>
              
              {/* Main App Routes */}
              <Route 
                path="/" 
                element={
                  <PrivateRoute>
                    <DashboardLayout />
                  </PrivateRoute>
                }
              >
                <Route index element={<Dashboard />} />
                <Route path="vehicle-arrival" element={<VehicleArrival />} />
                <Route path="vehicle-arrival/new" element={<NewVehicleArrival />} />
                <Route path="vehicle-arrival/view/:id" element={<ViewVehicleArrival />} />
                <Route path="vehicle-arrival/edit/:id" element={<EditVehicleArrival />} />
                <Route path="record-purchase" element={<RecordPurchase />} />
                <Route path="record-purchase/new" element={<NewRecordPurchase />} />
                <Route path="record-purchase/view/:id" element={<ViewRecordPurchase />} />
                <Route path="record-purchase/edit/:id" element={<EditRecordPurchase />} />
                <Route path="inventory" element={<Inventory />} />
                <Route path="sales" element={<Sales />} />
                <Route path="sales/new" element={<NewSale />} />
                <Route path="sales/view/:id" element={<ViewSale />} />
                <Route path="sales/edit/:id" element={<EditSale />} />
                <Route path="dispatch" element={<Dispatch />} />
                <Route path="suppliers" element={<Suppliers />} />
                <Route path="suppliers/new" element={<AddSupplier />} />
                <Route path="suppliers/view/:id" element={<ViewSupplier />} />
                <Route path="suppliers/edit/:id" element={<EditSupplier />} />
                <Route path="customers" element={<Customers />} />
                <Route path="customers/new" element={<AddCustomer />} />
                <Route path="customers/view/:id" element={<ViewCustomer />} />
                <Route path="customers/edit/:id" element={<EditCustomer />} />
                <Route path="ledger" element={<Transactions />} />
                <Route path="payments" element={<Payments />} />
              </Route>
              
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Router>
        </SidebarProvider>
      </AuthProvider>
    </AdminProvider>
  );
}

export default App;
