import React, { useState, useEffect } from 'react';
import { ShoppingCart, Search, Filter, Plus, FileText, Trash2, Eye, Pencil } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { getSalesOrders, deleteSalesOrder, getSalesAnalytics } from '../../services/api';
import { formatQuantityWithUnit } from '../../utils/weightUtils';
import MobileTable from '../../components/ui/MobileTable';
import Dropdown from '../../components/ui/Dropdown';

interface SalesOrderItem {
  product_name: string;
  sku_code: string;
  quantity: number;
  grn_quantity?: number;
  unit_type: string;
  unit_price: number;
  total_price: number;
}

interface SalesOrder {
  id: string;
  order_number: string;
  customer: {
    id: string;
    name: string;
    customer_type: string;
  };
  order_date: string;
  delivery_date: string | null;
  delivery_address: string | null;
  payment_mode: string;
  subtotal: number | null;
  tax_amount: number | null;
  discount_amount: number | null;
  total_amount: number | null;
  status: string;
  sale_type: string;
  items: SalesOrderItem[];
}

interface SalesAnalytics {
  totalSalesValue: number;
  totalProcessingValue: number;
  totalOrders: number;
  completedOrders: number;
  processingOrders: number;
  ordersByStatus: {
    processing: number;
    dispatched: number;
    delivered: number;
    completed: number;
    cancelled: number;
  };
  salesByPaymentMode: {
    cash: { value: number; count: number };
    credit: { value: number; count: number };
    online: { value: number; count: number };
    upi: { value: number; count: number };
    multiple: { value: number; count: number };
  };
}

const Sales: React.FC = () => {
  const navigate = useNavigate();
  const [sales, setSales] = useState<SalesOrder[]>([]);
  const [analytics, setAnalytics] = useState<SalesAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');

  useEffect(() => {
    loadSalesOrders();
    loadSalesAnalytics();
  }, []);

  const loadSalesOrders = async () => {
    try {
      const data = await getSalesOrders();
      
      // Add sale_type to each order and exclude pending approval orders
      const salesWithType = (data || [])
        .filter((order: SalesOrder) => order.status !== 'pending_approval') // Exclude pending approval orders
        .map((order: SalesOrder) => ({
          ...order,
          sale_type: order.delivery_date || order.delivery_address ? 'outstation' : 'counter'
        }));
      
      setSales(salesWithType);
    } catch (error) {
      console.error('Error loading sales orders:', error);
      // Global error handler will show the backend error message
    } finally {
      setLoading(false);
    }
  };

  const loadSalesAnalytics = async () => {
    try {
      const data = await getSalesAnalytics();
      setAnalytics(data);
    } catch (error) {
      console.error('Error loading sales analytics:', error);
      // Global error handler will show the backend error message
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const handleDeleteOrder = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this sales order?')) {
      try {
        await deleteSalesOrder(id);
        setSales(prev => prev.filter(order => order.id !== id));
        toast.success('Sales order deleted successfully');
      } catch (error) {
        console.error('Error deleting sales order:', error);
        // Global error handler will show the backend error message
      }
    }
  };

  const filteredSales = sales.filter(sale => {
    const matchesSearch = 
      sale.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.order_number.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesStatus = selectedStatus === 'all' || sale.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getSaleTypeColor = (saleType: string) => {
    return saleType === 'outstation' 
      ? 'bg-blue-100 text-blue-800' 
      : 'bg-green-100 text-green-800';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'pending_approval':
        return 'bg-orange-100 text-orange-800';
      case 'dispatched':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case 'processing':
        return 'Dispatch Pending';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'draft':
        return 'Draft';
      case 'pending_approval':
        return 'Pending Approval';
      case 'dispatched':
        return 'Dispatched';
      case 'delivered':
        return 'Delivered';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getPaymentModeDisplay = (mode: string) => {
    switch (mode) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'upi':
        return 'UPI';
      case 'cash':
        return 'Cash';
      case 'credit':
        return 'Credit';
      default:
        return mode.charAt(0).toUpperCase() + mode.slice(1);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading sales orders...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800">Sales Management</h1>
        </div>
        <button 
          onClick={() => navigate('/sales/new')}
          className="bg-green-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center w-full sm:w-auto"
        >
          <Plus className="h-4 w-4 mr-1" />
          New Sale
        </button>
      </div>

      {/* Sales Summary */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-white p-4 sm:p-5 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-500 truncate">Total Sales</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800 mt-1">
                {analyticsLoading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  `₹${(analytics?.totalSalesValue || 0).toLocaleString()}`
                )}
              </p>
              <p className="text-xs text-gray-400 mt-1">Completed orders only</p>
            </div>
            <div className="h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-3">
              <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6" />
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 sm:p-5 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-500 truncate">Total Orders</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800 mt-1">
                {analyticsLoading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  analytics?.totalOrders || 0
                )}
              </p>
              <p className="text-xs text-gray-400 mt-1">Active orders</p>
            </div>
            <div className="h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 flex-shrink-0 ml-3">
              <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 sm:p-5 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-500 truncate">Processing Orders</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800 mt-1">
                {analyticsLoading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  analytics?.processingOrders || 0
                )}
              </p>
              <p className="text-xs text-gray-400 mt-1 truncate">
                ₹{analyticsLoading ? '0' : (analytics?.totalProcessingValue || 0).toLocaleString()}
              </p>
            </div>
            <div className="h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600 flex-shrink-0 ml-3">
              <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 sm:p-5 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-500 truncate">Completed</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800 mt-1">
                {analyticsLoading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  analytics?.completedOrders || 0
                )}
              </p>
              <p className="text-xs text-gray-400 mt-1">Delivered & completed</p>
            </div>
            <div className="h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-3">
              <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0">
        <div className="relative flex-1 max-w-xs">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500"
            placeholder="Search orders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Dropdown
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'draft', label: 'Draft' },
                { value: 'processing', label: 'Processing' },
                { value: 'dispatched', label: 'Dispatched' },
                { value: 'delivered', label: 'Delivered' },
                { value: 'completed', label: 'Completed' },
                { value: 'cancelled', label: 'Cancelled' }
              ]}
              value={selectedStatus}
              onChange={setSelectedStatus}
              placeholder="Select status"
              className="text-sm"
            />
          </div>
        </div>
      </div>

      {/* Sales Table */}
      <MobileTable
        columns={[
          {
            key: 'orderDetails',
            label: 'Order Details',
            mobileLabel: 'Order',
            priority: 'high',
            render: (_, sale) => (
              <div className="flex items-center">
                <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                  <ShoppingCart className="h-5 w-5" />
                </div>
                <div className="ml-4 min-w-0 flex-1">
                  <div className="text-sm font-semibold text-gray-900 truncate">{sale.order_number}</div>
                  <div className="text-xs text-gray-500">{formatDateTime(sale.order_date)}</div>
                  {sale.delivery_date && (
                    <div className="text-xs text-blue-600 font-medium">
                      Delivery: {new Date(sale.delivery_date).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            )
          },
          {
            key: 'customer',
            label: 'Customer',
            mobileLabel: 'Customer',
            priority: 'high',
            render: (_, sale) => (
              <div className="min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">{sale.customer.name}</div>
                <div className="text-xs text-gray-500 capitalize">{sale.customer.customer_type}</div>
              </div>
            )
          },
          {
            key: 'total',
            label: 'Total',
            mobileLabel: 'Amount',
            priority: 'high',
            render: (_, sale) => (
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">₹{(sale.total_amount ?? 0).toLocaleString()}</div>
                <div className="text-xs text-gray-500">{getPaymentModeDisplay(sale.payment_mode)}</div>
              </div>
            )
          },
          {
            key: 'status',
            label: 'Status',
            mobileLabel: 'Status',
            priority: 'high',
            render: (_, sale) => (
              <span className={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${getStatusColor(sale.status)}`}>
                {getStatusDisplayText(sale.status)}
              </span>
            )
          },
          {
            key: 'saleType',
            label: 'Sale Type',
            mobileLabel: 'Type',
            priority: 'medium',
            render: (_, sale) => (
              <span className={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${getSaleTypeColor(sale.sale_type)}`}>
                {sale.sale_type === 'outstation' ? 'Outstation' : 'Counter'}
              </span>
            )
          },
          {
            key: 'items',
            label: 'Items',
            mobileLabel: 'Order Details',
            priority: 'medium',
            render: (_, sale) => (
              <div className="space-y-1">
                {(sale.items || []).slice(0, 3).map((item: SalesOrderItem, index: number) => {
                  const grnQuantity = item.grn_quantity ?? item.quantity;
                  const finalQuantity = grnQuantity !== null && grnQuantity !== undefined ? grnQuantity : item.quantity;
                  
                  return (
                    <div key={index} className="text-sm text-gray-700">
                      {item.product_name}-{item.sku_code}({formatQuantityWithUnit(finalQuantity, item.unit_type)})
                    </div>
                  );
                })}
                {(sale.items || []).length > 3 && (
                  <div className="text-sm text-gray-500 italic">
                    +{(sale.items || []).length - 3} more items
                  </div>
                )}
              </div>
            )
          },
          {
            key: 'actions',
            label: 'Actions',
            mobileLabel: 'Actions',
            priority: 'high',
            render: (_, sale) => (
              <div className="flex items-center justify-center space-x-3">
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/sales/view/${sale.id}`);
                  }}
                  className="p-2 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded-full transition-colors duration-150"
                  title="View Details"
                >
                  <Eye className="h-4 w-4" />
                </button>
                {(sale.status === 'draft' || sale.status === 'processing') && (
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/sales/edit/${sale.id}`);
                    }}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-full transition-colors duration-150"
                    title="Edit Order"
                  >
                    <Pencil className="h-4 w-4" />
                  </button>
                )}
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/sales/invoice/${sale.id}`);
                  }}
                  className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-full transition-colors duration-150"
                  title="Generate Invoice"
                >
                  <FileText className="h-4 w-4" />
                </button>
                {sale.status === 'draft' && (
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteOrder(sale.id);
                    }}
                    className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-full transition-colors duration-150"
                    title="Delete Order"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            )
          }
        ]}
        data={filteredSales}
        loading={loading}
        cardLayout="detailed"
        emptyState={
          <div className="py-12 text-center text-gray-500">
            <ShoppingCart className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Sales Orders Found</h3>
            <p className="text-sm text-gray-500 mb-4">
              {sales.length === 0 
                ? "Get started by creating your first sales order."
                : "No orders match your current search criteria."
              }
            </p>
            {sales.length === 0 && (
              <button
                onClick={() => navigate('/sales/new')}
                className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200"
              >
                Create First Sales Order
              </button>
            )}
          </div>
        }
      />
    </div>
  );
};

export default Sales;
