import React, { useState, useEffect } from 'react';
import { User, Search, Filter, Eye, Pencil, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { customerService, type Customer } from '../../services/api';
import Dropdown from '../../components/ui/Dropdown';

const Customers: React.FC = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      const data = await customerService.getAll();
      setCustomers(data || []);
    } catch (error) {
      console.error('Error loading customers:', error);
      // Global error handler will show the backend error message
    } finally {
      setLoading(false);
    }
  };
  
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = 
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.contact.includes(searchTerm);
      
    const matchesType = selectedType === 'all' || customer.customer_type === selectedType;
    const matchesStatus = selectedStatus === 'all' || customer.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const customerTypes = Array.from(new Set(customers.map(customer => customer.customer_type)));

  const handleStatusChange = async (id: string, newStatus: 'active' | 'inactive') => {
    try {
      await customerService.update(id, { status: newStatus });
      
      setCustomers(prev => prev.map(customer => 
        customer.id === id ? { ...customer, status: newStatus } : customer
      ));
      
      toast.success(`Customer ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Error updating customer status:', error);
      // Global error handler will show the backend error message
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading customers...</div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex items-center">
          <User className="h-6 w-6 text-green-600 mr-2" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Customer Management</h1>
        </div>
        <button 
          onClick={() => navigate('/customers/new')}
          className="bg-green-600 text-white rounded-md px-4 py-3 sm:py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center touch-manipulation"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Customer
        </button>
      </div>
      
      {/* Customer Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Customers</p>
              <p className="text-2xl font-bold text-gray-800">{customers.length}</p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
              <User className="h-5 w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Credit Extended</p>
              <p className="text-2xl font-bold text-gray-800">
                ₹{customers.reduce((sum, customer) => sum + (Number(customer.current_balance) || 0), 0).toLocaleString()}
              </p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
              <User className="h-5 w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Active Customers</p>
              <p className="text-2xl font-bold text-gray-800">
                {customers.filter(customer => customer.status === 'active').length}
              </p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
              <User className="h-5 w-5" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Filters and Search */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 text-base sm:text-sm border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500"
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <Dropdown
              options={[
                { value: 'all', label: 'All Types' },
                ...customerTypes.map(type => ({ value: type, label: type }))
              ]}
              value={selectedType}
              onChange={setSelectedType}
              placeholder="All Types"
              className="min-w-[140px]"
            />
          </div>
          <Dropdown
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' }
            ]}
            value={selectedStatus}
            onChange={setSelectedStatus}
            placeholder="All Status"
            className="min-w-[120px]"
          />
        </div>
      </div>
      
      {/* Customers Table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        {/* Desktop Table */}
        <div className="hidden lg:block">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer Info
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Details
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Credit Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                          <User className="h-5 w-5" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                          <div className="text-sm text-gray-500">{customer.customer_type}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        <div className="text-gray-900">{customer.contact}</div>
                        <div className="text-gray-500">{customer.email}</div>
                        <div className="text-gray-500 truncate max-w-xs">{customer.address}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">
                        <div className="text-gray-900">Balance: ₹{customer.current_balance.toLocaleString()}</div>
                        <div className="text-gray-500">Limit: ₹{customer.credit_limit.toLocaleString()}</div>
                        <div className="text-gray-500">Terms: {customer.payment_terms} days</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={customer.status}
                        onChange={(e) => handleStatusChange(customer.id, e.target.value as 'active' | 'inactive')}
                        className={`text-sm rounded-full px-3 py-1 border-0 focus:ring-2 focus:ring-green-500 ${
                          customer.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => navigate(`/customers/view/${customer.id}`)}
                          className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 p-1 touch-manipulation"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => navigate(`/customers/edit/${customer.id}`)}
                          className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-1 touch-manipulation"
                          title="Edit Customer"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden">
          <div className="divide-y divide-gray-200">
            {filteredCustomers.map((customer) => (
              <div key={customer.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                      <User className="h-5 w-5" />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.customer_type}</div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => navigate(`/customers/view/${customer.id}`)}
                      className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 p-2 touch-manipulation"
                      title="View Details"
                    >
                      <Eye className="h-5 w-5" />
                    </button>
                    <button 
                      onClick={() => navigate(`/customers/edit/${customer.id}`)}
                      className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-2 touch-manipulation"
                      title="Edit Customer"
                    >
                      <Pencil className="h-5 w-5" />
                    </button>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-500">Contact: </span>
                    <span className="text-gray-900">{customer.contact}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Email: </span>
                    <span className="text-gray-900">{customer.email}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Address: </span>
                    <span className="text-gray-900">{customer.address}</span>
                  </div>
                  <div className="flex flex-wrap gap-4 pt-2">
                    <div>
                      <span className="text-gray-500">Balance: </span>
                      <span className="text-gray-900 font-medium">₹{customer.current_balance.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Limit: </span>
                      <span className="text-gray-900">₹{customer.credit_limit.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Terms: </span>
                      <span className="text-gray-900">{customer.payment_terms} days</span>
                    </div>
                  </div>
                  <div className="pt-2">
                    <select
                      value={customer.status}
                      onChange={(e) => handleStatusChange(customer.id, e.target.value as 'active' | 'inactive')}
                      className={`text-sm rounded-full px-3 py-2 border-0 focus:ring-2 focus:ring-green-500 touch-manipulation ${
                        customer.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {filteredCustomers.length === 0 && !loading && (
          <div className="py-12 text-center text-gray-500">
            <User className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Customers Found</h3>
            <p className="text-sm text-gray-500 mb-4">
              {customers.length === 0 
                ? "Get started by adding your first customer."
                : "No customers match your current search and filter criteria."
              }
            </p>
            {customers.length === 0 && (
              <button
                onClick={() => navigate('/customers/new')}
                className="bg-green-600 text-white rounded-md px-4 py-3 text-sm font-medium hover:bg-green-700 transition-colors duration-200 touch-manipulation"
              >
                Add First Customer
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Customers;
