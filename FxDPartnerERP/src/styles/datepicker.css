/* Enhanced styles for react-datepicker with modern UI */

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .react-datepicker {
    font-size: 0.95rem;
    width: 100%;
    max-width: 320px;
  }
  
  .react-datepicker__month-container {
    width: 100%;
  }
  
  .react-datepicker__month {
    margin: 0;
    padding: 0;
  }
  
  .react-datepicker__day-names {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 4px;
  }
  
  .react-datepicker__week {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 2px;
  }
  
  .react-datepicker__day-name {
    width: auto;
    height: 2.5rem;
    line-height: 2.5rem;
    margin: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .react-datepicker__day {
    width: auto;
    height: 2.5rem;
    line-height: 2.5rem;
    margin: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }
  
  .react-datepicker__time-container {
    width: 100px;
  }
  
  .react-datepicker__time-list-item {
    padding: 0.5rem;
    width: auto;
    height: auto;
    line-height: normal;
  }
}

/* General improvements for all screen sizes */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container {
  width: 100%;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0.375rem 0.375rem 0 0;
  padding-top: 0.5rem;
}

.react-datepicker__current-month {
  font-weight: 600;
  color: #000000;
  margin-bottom: 0.5rem;
}

.react-datepicker__day-name {
  color: #000000;
  font-weight: 500;
}

.react-datepicker__day {
  color: #000000;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.react-datepicker__day:hover {
  background-color: #f3f4f6;
}

.react-datepicker__day--selected {
  background-color: #16a34a !important;
  color: white !important;
  font-weight: 500;
}

.react-datepicker__day--selected:hover {
  background-color: #15803d !important;
}

.react-datepicker__day--keyboard-selected {
  background-color: #dcfce7;
  color: #16a34a;
}

.react-datepicker__day--today {
  font-weight: 600;
  color: #16a34a;
}

.react-datepicker__day--disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent;
}

.react-datepicker__time-container {
  border-left: 1px solid #e5e7eb;
}

.react-datepicker__time-list-item {
  transition: all 0.2s;
}

.react-datepicker__time-list-item:hover {
  background-color: #f3f4f6;
}

.react-datepicker__time-list-item--selected {
  background-color: #16a34a !important;
  color: white !important;
  font-weight: 500;
}

.react-datepicker__time-list-item--selected:hover {
  background-color: #15803d !important;
}

.react-datepicker__navigation {
  top: 0.75rem;
}

.react-datepicker__navigation-icon::before {
  border-color: #6b7280;
}

.react-datepicker__navigation:hover .react-datepicker__navigation-icon::before {
  border-color: #374151;
}

/* Improve touch targets for mobile */
@media (pointer: coarse) {
  .react-datepicker__day,
  .react-datepicker__time-list-item {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Portal positioning for mobile - clean without overlays */
.react-datepicker-popper {
  z-index: 1000;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
}

.react-datepicker-popper::before,
.react-datepicker-popper::after {
  display: none !important;
}

/* Remove all possible shadow elements */
.react-datepicker-popper .react-datepicker__triangle {
  display: none !important;
}

.react-datepicker-popper .react-datepicker__triangle::before,
.react-datepicker-popper .react-datepicker__triangle::after {
  display: none !important;
}

/* Override any default react-datepicker overlay styles */
.react-datepicker-popper[data-placement] {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

@media (max-width: 640px) {
  .react-datepicker-popper[data-placement^="bottom"] {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  
  .react-datepicker-popper[data-placement^="top"] {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
  }
  
  /* Clean positioning near input field - absolute position for mobile */
  .react-datepicker-popper {
    position: absolute !important;
    z-index: 1000 !important;
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    max-width: 95vw !important;
    max-height: 95vh !important;
  }
  
  .react-datepicker {
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-width: 320px;
    max-height: 400px;
    overflow: auto;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
  }
}

/* Additional mobile specific positioning */
@media (max-width: 640px) {
  .dashboard-date-input .react-datepicker-popper {
    position: absolute !important;
    z-index: 1000 !important;
    width: auto !important;
    max-width: 320px !important;
  }
}

/* Dashboard specific styles for compact date inputs */
.dashboard-date-input input {
  font-size: 0.75rem !important;
  padding: 0.25rem 0.5rem !important;
  height: 2rem !important;
  min-height: 2rem !important;
  color: #111827 !important;
  background-color: #ffffff !important;
}

.dashboard-date-input .react-datepicker__input-container input {
  font-size: 0.75rem !important;
  padding: 0.25rem 0.5rem !important;
  height: 2rem !important;
  min-height: 2rem !important;
  text-align: center;
  color: #111827 !important;
  background-color: #ffffff !important;
}

/* Ensure placeholder text is visible */
.dashboard-date-input input::placeholder {
  color: #9ca3af !important;
  opacity: 1;
}

.dashboard-date-input .react-datepicker__input-container input::placeholder {
  color: #9ca3af !important;
  opacity: 1;
}

/* Dashboard Mobile Calendar Styles */
.dashboard-mobile-calendar {
  width: 100% !important;
  margin: 0 !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.dashboard-mobile-calendar .react-datepicker__header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 0.5rem 0.5rem;
  border-radius: 0.5rem 0.5rem 0 0;
}

.dashboard-mobile-calendar .react-datepicker__current-month {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dashboard-mobile-calendar .react-datepicker__day-names {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 4px;
}

.dashboard-mobile-calendar .react-datepicker__week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 4px;
}

.dashboard-mobile-calendar .react-datepicker__day-name {
  width: auto;
  height: 2.5rem;
  line-height: 2.5rem;
  margin: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
}

.dashboard-mobile-calendar .react-datepicker__day {
  width: auto;
  height: 2.5rem;
  line-height: 2.5rem;
  margin: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  min-height: 40px;
  min-width: 40px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dashboard-mobile-calendar .react-datepicker__day:hover {
  background-color: #f3f4f6;
}

.dashboard-mobile-calendar .react-datepicker__day--selected {
  background-color: #16a34a !important;
  color: white !important;
  font-weight: 600;
}

.dashboard-mobile-calendar .react-datepicker__day--today {
  background-color: #dcfce7;
  color: #16a34a;
  font-weight: 600;
}

.dashboard-mobile-calendar .react-datepicker__navigation {
  top: 1rem;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
}

.dashboard-mobile-calendar .react-datepicker__navigation:hover {
  background-color: #f3f4f6;
}

.dashboard-mobile-calendar .react-datepicker__navigation-icon::before {
  border-color: #6b7280;
  border-width: 2px 2px 0 0;
}

/* Dashboard Desktop Calendar Styles */
.dashboard-desktop-popper {
  z-index: 1000 !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.dashboard-desktop-calendar {
  font-size: 0.875rem;
  max-width: 300px;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.dashboard-desktop-calendar .react-datepicker__header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 0.75rem 0.5rem 0.5rem;
}

.dashboard-desktop-calendar .react-datepicker__current-month {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dashboard-desktop-calendar .react-datepicker__day-name {
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
}

.dashboard-desktop-calendar .react-datepicker__day {
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.dashboard-desktop-calendar .react-datepicker__day:hover {
  background-color: #f3f4f6;
}

.dashboard-desktop-calendar .react-datepicker__day--selected {
  background-color: #16a34a !important;
  color: white !important;
  font-weight: 600;
}

.dashboard-desktop-calendar .react-datepicker__day--today {
  background-color: #dcfce7;
  color: #16a34a;
  font-weight: 600;
}

.dashboard-desktop-calendar .react-datepicker__navigation {
  top: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
}

.dashboard-desktop-calendar .react-datepicker__navigation-icon::before {
  border-color: #6b7280;
  border-width: 1px 1px 0 0;
}

.dashboard-desktop-calendar .react-datepicker__navigation:hover .react-datepicker__navigation-icon::before {
  border-color: #374151;
}

/* Responsive adjustments for desktop popper */
@media (max-width: 1024px) {
  .dashboard-desktop-popper {
    position: fixed !important;
    z-index: 1000 !important;
  }
  
  .dashboard-desktop-calendar {
    max-width: 280px;
  }
}
