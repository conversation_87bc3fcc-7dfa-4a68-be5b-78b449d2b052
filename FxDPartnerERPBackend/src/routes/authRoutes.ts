import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// Authentication routes
router.post('/login', AuthController.login);
router.post('/logout', authenticateToken, AuthController.logout);
router.get('/verify', authenticateToken, AuthController.verify);
router.get('/me', authenticateToken, AuthController.getCurrentUser);
router.post('/switch-organization/:organizationId', authenticateToken, AuthController.switchOrganization);

export default router;
